import Elysia from "elysia";
import { cors } from "@elysiajs/cors";
import { staticPlugin } from "@elysiajs/static";

import { serve } from "bun";

import index from "@frontend/index.html";

import { proxyRoute } from "./proxy/route";

const app = new Elysia().use(cors()).use(staticPlugin()).use(proxyRoute);

const server = serve({
  routes: {
    "/": index,
  },
  fetch: app.fetch,
  development: process.env.NODE_ENV !== "production" && {
    // Enable browser hot reloading in development
    hmr: true,

    // Echo console logs from the browser to the server
    console: true,
  },
});

console.log(`🚀 Server running at ${server.url}`);
