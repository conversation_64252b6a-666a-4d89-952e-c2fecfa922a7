{"name": "bun-react-template", "version": "0.1.0", "private": true, "type": "module", "main": "src/index.tsx", "module": "src/index.tsx", "scripts": {"dev": "bun --hot src/index.tsx", "start": "NODE_ENV=production bun src/index.tsx", "build": "bun run build.ts"}, "dependencies": {"@base-ui-components/react": "^1.0.0-beta.1", "@elysiajs/cors": "^1.3.3", "@elysiajs/static": "^1.3.0", "@hookform/resolvers": "^4.1.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "bun-plugin-tailwind": "^0.0.14", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "elysia": "^1.3.5", "lucide-react": "^0.475.0", "react": "^19", "react-dom": "^19", "react-hook-form": "^7.54.2", "tailwind-merge": "^3.0.1", "tailwindcss": "^4.0.6", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/bun": "^1.2.18", "@types/react": "^19", "@types/react-dom": "^19"}}