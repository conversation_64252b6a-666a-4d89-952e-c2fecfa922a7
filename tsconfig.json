{
  "compilerOptions": {
    "jsx": "react-jsx",
    "allowJs": true,

    // Bundler mode
    "target": "esnext",
    "moduleResolution": "bundler",
    "module": "Preserve",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "noEmit": true,

    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@frontend/*": ["./src/frontend/*"],
      "@public/*": ["./public/*"]
    }
  },
  "include": ["**/*.ts", "**/*.tsx"],
  "exclude": ["dist", "node_modules"]
}
